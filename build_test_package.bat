@echo off
echo Building and testing koalafolio package...

REM Clean previous builds
rmdir /s /q dist
rmdir /s /q build
rmdir /s /q test_venv

REM Build package
python setup.py sdist bdist_wheel
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    exit /b 1
)

REM Check package
python -m twine check dist/*
if %ERRORLEVEL% neq 0 (
    echo Package check failed!
    exit /b 1
)

REM Upload to Test PyPI
python -m twine upload --repository testpypi dist/*
if %ERRORLEVEL% neq 0 (
    echo Upload to Test PyPI failed!
    exit /b 1
)

REM Create virtual environment for testing
python -m venv test_venv
call test_venv\Scripts\activate.bat

REM Install package dependencies first (since test<PERSON><PERSON> might not have all dependencies)
pip install ccxt openpyxl "pandas>=1.5.3" "pycoingecko<=2.3.0" pycryptodomex pyqt5 pyqtchart requests "tzlocal>=5.0.1" xlrd pytest

REM Install the package from Test PyPI
pip install --index-url https://test.pypi.org/simple/ --no-deps koalafolio

REM Basic import test
python -c "from koalafolio import gui_root; print('Package import successful!')"
if %ERRORLEVEL% neq 0 (
    echo Package import test failed!
    deactivate
    exit /b 1
)

REM Run test suite
python -m pytest koalafolio/tests/test_converter.py koalafolio/tests/test_exchanges.py
if %ERRORLEVEL% neq 0 (
    echo Test suite failed!
    deactivate
    exit /b 1
)

echo Package build, upload and test completed successfully!
deactivate

REM Clean up test environment
rmdir /s /q test_venv