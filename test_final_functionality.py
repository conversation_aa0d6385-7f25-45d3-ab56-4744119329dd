#!/usr/bin/env python3
"""
Final test of the API combobox functionality
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import PyQt5.QtWidgets as qtwidgets
    import PyQt5.QtCore as qtcore
    from koalafolio.gui.QApiImport import Api<PERSON>temModel, FilterableApiModel
    
    class MockApiModel:
        def __init__(self, name, api_type):
            self.apiType = api_type
    
    def test_complete_functionality():
        """Test the complete functionality"""
        print("Testing complete API filtering functionality...")
        
        app = qtwidgets.QApplication([])
        
        # Create mock API models (similar to real ones)
        mock_apis = {
            'binance': MockApiModel('binance', 'exchange'),
            'kraken': MockApiModel('kraken', 'exchange'),
            'coinbase': MockApiModel('coinbase', 'exchange'),
            'coinbasepro': MockApiModel('coinbasepro', 'exchange'),
            'ethereum': <PERSON>ckApiModel('ethereum', 'chaindata'),
            'cardano': MockApiModel('cardano', 'chaindata'),
            'solana': MockApiModel('solana', 'chaindata'),
            'polkadot': MockApiModel('polkadot', 'chaindata'),
        }
        
        # Create the models
        item_model = ApiItemModel()
        filter_model = FilterableApiModel()
        filter_model.setSourceModel(item_model)
        item_model.setApis(mock_apis)
        
        print(f"✓ Created models with {item_model.rowCount()} APIs")
        
        # Create combobox like in the real application
        combo = qtwidgets.QComboBox()
        combo.setEditable(True)
        combo.setInsertPolicy(qtwidgets.QComboBox.NoInsert)
        combo.setModel(filter_model)
        
        # Set up completer
        completer = qtwidgets.QCompleter(filter_model)
        completer.setCaseSensitivity(qtcore.Qt.CaseInsensitive)
        completer.setFilterMode(qtcore.Qt.MatchContains)
        combo.setCompleter(completer)
        
        print(f"✓ Combobox setup complete with {combo.count()} items")
        
        # Test text filtering
        print("\nTesting text filtering:")
        filter_model.setTextFilter("coin")
        print(f"  - Filter 'coin': {filter_model.rowCount()} results")
        
        filter_model.setTextFilter("eth")
        print(f"  - Filter 'eth': {filter_model.rowCount()} results")
        
        filter_model.setTextFilter("")
        print(f"  - Clear filter: {filter_model.rowCount()} results")
        
        # Test type filtering
        print("\nTesting type filtering:")
        filter_model.setTypeFilter(show_exchange=True, show_chaindata=False)
        exchange_count = filter_model.rowCount()
        print(f"  - Exchange only: {exchange_count} results")
        
        filter_model.setTypeFilter(show_exchange=False, show_chaindata=True)
        chaindata_count = filter_model.rowCount()
        print(f"  - Chaindata only: {chaindata_count} results")
        
        filter_model.setTypeFilter(show_exchange=True, show_chaindata=True)
        all_count = filter_model.rowCount()
        print(f"  - All types: {all_count} results")
        
        # Test combined filtering
        print("\nTesting combined filtering:")
        filter_model.setTypeFilter(show_exchange=True, show_chaindata=False)
        filter_model.setTextFilter("coin")
        combined_count = filter_model.rowCount()
        print(f"  - Exchange + 'coin': {combined_count} results")
        
        # Test line edit functionality
        print("\nTesting line edit:")
        line_edit = combo.lineEdit()
        print(f"✓ Line edit available: {line_edit is not None}")
        
        if line_edit:
            # Simulate typing
            line_edit.setText("binance")
            print(f"✓ Can set text: '{line_edit.text()}'")
            
            line_edit.clear()
            print(f"✓ Can clear text: '{line_edit.text()}'")
        
        # Verify expected counts
        assert exchange_count == 4, f"Expected 4 exchanges, got {exchange_count}"
        assert chaindata_count == 4, f"Expected 4 chaindata, got {chaindata_count}"
        assert all_count == 8, f"Expected 8 total, got {all_count}"
        assert combined_count == 2, f"Expected 2 exchange with 'coin', got {combined_count}"
        
        print("\n🎉 All functionality tests passed!")
        print("\nThe implementation should now work correctly:")
        print("  - Users can type to search APIs")
        print("  - Checkboxes filter by API type")
        print("  - Both filters work together")
        print("  - Text input is visible and functional")
        
        return True
    
    if __name__ == "__main__":
        test_complete_functionality()

except ImportError as e:
    print(f"Import error: {e}")
    print("This is expected if dependencies are missing.")
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
