#!/usr/bin/env python3
"""
Test the text display functionality of the editable combobox
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import PyQt5.QtWidgets as qtwidgets
    import PyQt5.QtCore as qtcore
    from koalafolio.gui.QApiImport import A<PERSON><PERSON>temModel, FilterableApiModel
    
    class MockApiModel:
        def __init__(self, name, api_type):
            self.apiType = api_type
    
    def test_text_display():
        """Test that text is properly displayed in the editable combobox"""
        print("Testing text display functionality...")
        
        app = qtwidgets.QApplication([])
        
        # Create mock API models
        mock_apis = {
            'binance': MockApiModel('binance', 'exchange'),
            'kraken': MockApiModel('kraken', 'exchange'),
            'coinbase': MockApiModel('coinbase', 'exchange'),
            'ethereum': MockApiModel('ethereum', 'chaindata'),
        }
        
        # Create the models
        item_model = ApiItemModel()
        filter_model = FilterableApiModel()
        filter_model.setSourceModel(item_model)
        item_model.setApis(mock_apis)
        
        # Create combobox exactly like in the application
        combo = qtwidgets.QComboBox()
        combo.setEditable(True)
        combo.setInsertPolicy(qtwidgets.QComboBox.NoInsert)
        combo.setModel(filter_model)
        
        print(f"✓ Created combobox with {combo.count()} items")
        
        # Test initial setup
        if filter_model.rowCount() > 0:
            combo.setCurrentIndex(0)
            first_item = filter_model.data(filter_model.index(0, 0), qtcore.Qt.DisplayRole)
            if first_item:
                combo.lineEdit().setText(first_item)
                print(f"✓ Set initial text: '{combo.lineEdit().text()}'")
        
        # Test manual text setting
        combo.lineEdit().setText("test_text")
        print(f"✓ Manual text setting: '{combo.lineEdit().text()}'")
        
        # Test clearing
        combo.lineEdit().clear()
        print(f"✓ Text after clear: '{combo.lineEdit().text()}'")
        
        # Test setting to a valid API name
        combo.lineEdit().setText("binance")
        print(f"✓ Set to 'binance': '{combo.lineEdit().text()}'")
        
        # Test currentText method
        current_text = combo.currentText()
        print(f"✓ currentText(): '{current_text}'")
        
        # Test line edit properties
        line_edit = combo.lineEdit()
        print(f"✓ Line edit enabled: {line_edit.isEnabled()}")
        print(f"✓ Line edit visible: {line_edit.isVisible()}")
        print(f"✓ Line edit read-only: {line_edit.isReadOnly()}")
        
        # Test with different selections
        for i in range(min(3, filter_model.rowCount())):
            combo.setCurrentIndex(i)
            item_text = filter_model.data(filter_model.index(i, 0), qtcore.Qt.DisplayRole)
            combo.lineEdit().setText(item_text)
            print(f"✓ Index {i}: '{combo.lineEdit().text()}'")
        
        print("\n🎉 Text display test completed!")
        print("If you're still not seeing text, the issue might be:")
        print("1. Qt theme/style issues")
        print("2. Font/color settings")
        print("3. Widget focus issues")
        print("4. Platform-specific rendering problems")
        
        return True
    
    if __name__ == "__main__":
        test_text_display()

except ImportError as e:
    print(f"Import error: {e}")
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
