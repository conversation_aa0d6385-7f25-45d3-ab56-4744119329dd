#!/usr/bin/env python3
"""
Quick test to verify the new API filtering functionality
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import PyQt5.QtWidgets as qtwidgets
import PyQt5.QtCore as qtcore
from koalafolio.gui.QApiImport import Api<PERSON>temModel, FilterableApiModel

class MockApiModel:
    def __init__(self, name, api_type):
        self.apiType = api_type

def test_filterable_model():
    """Test the filterable API model functionality"""
    app = qtwidgets.QApplication([])
    
    # Create mock API models
    mock_apis = {
        'binance': MockApiModel('binance', 'exchange'),
        'kraken': MockApiModel('kraken', 'exchange'),
        'coinbase': MockApiModel('coinbase', 'exchange'),
        'ethereum': MockApiModel('ethereum', 'chaindata'),
        'cardano': <PERSON>ckApiModel('cardano', 'chaindata'),
        'solana': <PERSON>ckApiModel('solana', 'chaindata'),
    }
    
    # Create the models
    item_model = ApiItemModel()
    filter_model = FilterableApiModel()
    filter_model.setSourceModel(item_model)
    
    # Set the APIs
    item_model.setApis(mock_apis)
    
    print(f"Total APIs: {item_model.rowCount()}")
    print(f"Filtered APIs (all): {filter_model.rowCount()}")
    
    # Test filtering by type - only exchanges
    filter_model.setTypeFilter(show_exchange=True, show_chaindata=False)
    print(f"Filtered APIs (exchange only): {filter_model.rowCount()}")
    
    # Test filtering by type - only chaindata
    filter_model.setTypeFilter(show_exchange=False, show_chaindata=True)
    print(f"Filtered APIs (chaindata only): {filter_model.rowCount()}")
    
    # Test text filtering
    filter_model.setTypeFilter(show_exchange=True, show_chaindata=True)
    filter_model.setFilterFixedString("coin")
    print(f"Filtered APIs (containing 'coin'): {filter_model.rowCount()}")
    
    # List filtered results
    for i in range(filter_model.rowCount()):
        index = filter_model.index(i, 0)
        name = filter_model.data(index, qtcore.Qt.DisplayRole)
        print(f"  - {name}")
    
    print("Test completed successfully!")

if __name__ == "__main__":
    test_filterable_model()
