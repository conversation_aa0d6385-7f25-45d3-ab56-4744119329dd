# a test file for a filterable combobox widget
# layout: text, combobox, filterbutton
# filterbutton opens a popup with filter checkboxes

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QComboBox, QPushButton, QHBoxLayout, QVBoxLayout,
    QDialog, QCheckBox, QDialogButtonBox, QFormLayout, QMenu, QAction
)
from PyQt5.QtCore import Qt, QSortFilterProxyModel, QAbstractListModel, QModelIndex, QVariant, QPoint
from koalafolio.gui.widgets.QCompleterComboBox import QCompleterComboBoxView

class StringPropertyListModel(QAbstractListModel):
    def __init__(self, string_list, properties_list, parent=None):
        super().__init__(parent)
        self._strings = string_list
        self._properties = properties_list

    def rowCount(self, parent=QModelIndex()):
        return len(self._strings)

    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid():
            return QVariant()
        row = index.row()
        if role == Qt.DisplayRole:
            return self._strings[row]
        if role == Qt.UserRole:
            return self._properties[row]
        return QVariant()


class MultiValueFilterPopup(QDialog):
    def __init__(self, key, values, selected_values, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"Filter: {key}")
        self.selected_values = set(selected_values)
        self.checkboxes = {}
        layout = QVBoxLayout(self)
        for value in values:
            cb = QCheckBox(str(value), self)
            cb.setChecked(value in self.selected_values)
            self.checkboxes[value] = cb
            layout.addWidget(cb)
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel, self)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def get_selected(self):
        return [v for v, cb in self.checkboxes.items() if cb.isChecked()]

class FilterProxyModel(QSortFilterProxyModel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.active_filters = {}  # key: set of allowed values

    def set_filter(self, key, allowed_values):
        self.active_filters[key] = set(allowed_values)
        self.invalidateFilter()

    def clear_filter(self, key):
        if key in self.active_filters:
            del self.active_filters[key]
            self.invalidateFilter()

    def filterAcceptsRow(self, source_row, source_parent):
        model = self.sourceModel()
        props = model.data(model.index(source_row, 0), Qt.UserRole)
        for key, allowed in self.active_filters.items():
            if allowed and props.get(key) not in allowed:
                return False
        return True

class FilterableComboBoxView(QWidget):
    def __init__(self, label_text="", parent=None):
        super().__init__(parent)
        self.proxyModel = FilterProxyModel(self)
        self.filter_properties = {}  # key: list of possible values
        self.active_filter_values = {}  # key: set of currently enabled values

        self.label = QLabel(label_text, self)
        self.combobox = QCompleterComboBoxView(self)
        self.combobox.setModel(self.proxyModel)

        self.filter_buttons = {}  # key: QPushButton

        layout = QHBoxLayout(self)
        layout.addWidget(self.label)
        layout.addWidget(self.combobox)
        self.filter_buttons_layout = QHBoxLayout()
        layout.addLayout(self.filter_buttons_layout)
        self.setLayout(layout)

    def setModel(self, model):
        self.proxyModel.setSourceModel(model)
        self.generateFilterProperties(model)
        self.createFilterButtons()

    def generateFilterProperties(self, model):
        # Only accept key: list properties
        self.filter_properties.clear()
        for row in range(model.rowCount()):
            props = model.data(model.index(row, 0), Qt.UserRole)
            for key, value in props.items():
                if key not in self.filter_properties:
                    self.filter_properties[key] = []
                if value not in self.filter_properties[key]:
                    self.filter_properties[key].append(value)
        # At start, all values are enabled
        self.active_filter_values = {k: set(v) for k, v in self.filter_properties.items()}

    def createFilterButtons(self):
        # Remove old buttons
        for btn in self.filter_buttons.values():
            self.filter_buttons_layout.removeWidget(btn)
            btn.deleteLater()
        self.filter_buttons.clear()
        # Create new buttons
        for key, values in self.filter_properties.items():
            btn = QPushButton(key, self)
            btn.setCheckable(False)
            btn.clicked.connect(lambda checked, k=key: self.open_filter_popup(k))
            self.filter_buttons_layout.addWidget(btn)
            self.filter_buttons[key] = btn

    def open_filter_popup(self, key):
        values = self.filter_properties[key]
        selected = self.active_filter_values.get(key, set(values))
        dlg = MultiValueFilterPopup(key, values, selected, self)
        if dlg.exec_():
            selected_values = dlg.get_selected()
            self.active_filter_values[key] = set(selected_values)
            self.proxyModel.set_filter(key, selected_values)

    def model(self):
        return self.proxyModel

# Simple test window with default data
def main():
    app = QApplication(sys.argv)
    string_list = ["Apple", "Banana", "Carrot", "Date", "Eggplant"]
    properties_list = [
        {"type": "Fruit", "color": "red"},
        {"type": "Fruit", "color": "yellow"},
        {"type": "Vegetable", "color": "orange"},
        {"type": "Fruit", "color": "orange"},
        {"type": "Vegetable", "color": "yellow"},
    ]
    filter_properties = {
        "type": ["Fruit", "Vegetable"],
        "organic": True  # just a placeholder, type is bool
    }
    model = StringPropertyListModel(string_list, properties_list)
    window = QWidget()
    layout = QVBoxLayout(window)
    filterable = FilterableComboBoxView(label_text="Select:", parent=window)
    filterable.setModel(model)
    layout.addWidget(filterable)
    window.setWindowTitle("Filterable ComboBox Test")
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

